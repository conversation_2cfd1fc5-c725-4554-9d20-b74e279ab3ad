<!doctype html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🤔</text></svg>" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>How to Reply? 🤔 Генератор ответов на сообщения от ИИ</title>

    <!-- Basic Meta Tags -->
    <meta name="description" content="Не знаете, что ответить? Загрузите скриншот чата или введите сообщение, и наш ИИ сгенерирует идеальный ответ. Быстро, понятно, стильно." />
    <meta name="keywords" content="ИИ, чат, ответ, генератор, сообщения, скриншот, помощь, общение, AI, chat, reply, generator, messages, screenshot, communication" />
    <meta name="author" content="How to Reply?" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="How to Reply? 🤔 Генератор ответов на сообщения от ИИ" />
    <meta property="og:description" content="Не знаете, что ответить? Загрузите скриншот чата или введите сообщение, и наш ИИ сгенерирует идеальный ответ. Быстро, понятно, стильно." />
    <meta property="og:image" content="https://placehold.co/1200x630/3AAFF0/FFFFFF?text=How+to+Reply%3F" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="How to Reply? 🤔 Генератор ответов на сообщения от ИИ" />
    <meta name="twitter:description" content="Не знаете, что ответить? Загрузите скриншот чата или введите сообщение, и наш ИИ сгенерирует идеальный ответ. Быстро, понятно, стильно." />
    <meta name="twitter:image" content="https://placehold.co/1200x630/3AAFF0/FFFFFF?text=How+to+Reply%3F" />

    <!-- Theme Color -->
    <meta name="theme-color" content="#3AAFF0" />

    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://generativelanguage.googleapis.com" />
    <link rel="preconnect" href="https://placehold.co" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
